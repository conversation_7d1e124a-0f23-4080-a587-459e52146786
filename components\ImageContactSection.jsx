"use client";

import { Box, Grid, Typography, Container, Button, Paper } from "@mui/material";
import Image from "next/image";
import React from "react";
import { BsWhatsapp } from "react-icons/bs";
import pxToRem from "@/ThemeRegistry/pxToRem";

function ImageContactSection() {
  const images = [
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/1SSfmxI2FCwLG0Ce3np27J/bb423073b0f0099c918540962f799684/hookah.jpg",
      alt: "Vibes Rooftop Hookah Experience",
    },
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/56DVoyLC2c2CexEqKJH2Ky/bb902e800d824f0ebc96e2d065e609d4/photo_2023-11-06_12-25-23.jpg",
      alt: "Vibes Rooftop Atmosphere",
    },
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/7ooWbJ06KiPYPrVxrKfNNi/35e992f2c15769cc80c822a764ecb537/IMG_0007.JPG",
      alt: "Vibes Rooftop Interior",
    },
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/5rsv75gR8HM4oDvAkVjCph/b6911ea7e25f716e792ddabf50fd7162/cooktail.jpg",
      alt: "Vibes Rooftop Cocktails",
    },
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/4vehjxrR07huEAfiAAgV05/416c531c3f923598a7ecb4656baf6ff2/drink.jpg",
      alt: "Vibes Rooftop Drinks",
    },
    {
      src: "https://images.ctfassets.net/87jhdyn6f199/1WYNI0v3rkwMZvJPhfG4cm/42c7620097d4c702939417f594f84d19/vibes.jpg",
      alt: "Vibes Rooftop Experience",
    },
  ];

  return (
    <Box
      component="section"
      sx={{
        position: "relative",
        background: `
          radial-gradient(circle at 25% 75%, rgba(103, 247, 86, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 75% 25%, rgba(74, 29, 31, 0.08) 0%, transparent 50%),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
        py: { xs: 8, md: 12 },
        overflow: "hidden",
      }}
    >
      {/* Floating background elements */}
      <Box
        sx={{
          position: "absolute",
          top: "15%",
          left: "5%",
          width: "250px",
          height: "250px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.04) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(50px)",
          animation: "float 20s ease-in-out infinite",
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: "20%",
          right: "10%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 15s ease-in-out infinite reverse",
          zIndex: 1,
        }}
      />

      <Container sx={{ position: "relative", zIndex: 2 }}>
        {/* Modern Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 6, md: 8 } }}>
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              backgroundColor: "rgba(103, 247, 86, 0.1)",
              border: "1px solid rgba(103, 247, 86, 0.3)",
              borderRadius: "50px",
              px: 4,
              py: 1.5,
              mb: 3,
              backdropFilter: "blur(10px)",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#67f756",
                fontWeight: "600",
                fontSize: pxToRem(14),
                letterSpacing: "0.5px",
                textTransform: "uppercase",
              }}
            >
              Galerie & Kontakt
            </Typography>
          </Box>

          <Typography
            variant="h2"
            sx={{
              fontSize: `clamp(${pxToRem(28)}, 5vw, ${pxToRem(42)})`,
              fontWeight: 700,
              mb: 2,
              background: "linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              lineHeight: 1.2,
            }}
          >
            Erlebe Vibes Rooftop
          </Typography>
        </Box>

        <Grid container spacing={{ xs: 4, md: 6 }} alignItems="center">
          {/* Modern Image Gallery - Original 6-grid layout */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={{ xs: 3, md: 4 }}>
              {images.map((image, index) => (
                <Grid item xs={6} md={4} key={index}>
                  <Box
                    className="image-container"
                    sx={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      background: "rgba(255, 255, 255, 0.03)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                      cursor: "pointer",
                      height: { xs: "180px", md: "220px" },
                      position: "relative",
                      "&:hover": {
                        transform: "translateY(-8px) scale(1.05)",
                        boxShadow: "0 20px 40px rgba(103, 247, 86, 0.2)",
                        border: "1px solid rgba(103, 247, 86, 0.4)",
                      },
                    }}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="image"
                      style={{ borderRadius: "20px" }}
                    />
                    {/* Modern overlay effect */}
                    <Box
                      sx={{
                        position: "absolute",
                        inset: 0,
                        background: `
                          linear-gradient(135deg,
                            rgba(103,247,86,0.05) 0%,
                            transparent 30%,
                            transparent 70%,
                            rgba(74,29,31,0.05) 100%
                          )
                        `,
                        borderRadius: "20px",
                        opacity: 0,
                        transition: "opacity 0.3s ease",
                        pointerEvents: "none",
                        "&:hover": {
                          opacity: 1,
                        },
                      }}
                    />
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Modern Contact Section */}
          <Grid item xs={12} md={4}>
            <Box sx={{ pl: { md: 4 } }}>
              <Paper
                sx={{
                  background: "rgba(255, 255, 255, 0.05)",
                  backdropFilter: "blur(15px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  borderRadius: "24px",
                  p: { xs: 4, md: 5 },
                  textAlign: "center",
                  transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: "0 25px 50px rgba(103, 247, 86, 0.15)",
                    border: "1px solid rgba(103, 247, 86, 0.3)",
                    background: "rgba(255, 255, 255, 0.08)",
                  },
                }}
              >
                {/* Contact Header */}
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontSize: `clamp(${pxToRem(20)}, 4vw, ${pxToRem(28)})`,
                      fontWeight: 700,
                      mb: 2,
                      background:
                        "linear-gradient(135deg, #67f756 0%, #5ce646 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      lineHeight: 1.3,
                    }}
                  >
                    Kontaktiere uns
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "text.secondary",
                      fontSize: pxToRem(16),
                      lineHeight: 1.6,
                    }}
                  >
                    Schnell und einfach über WhatsApp erreichen
                  </Typography>
                </Box>

                {/* WhatsApp Contact Button */}
                <a
                  href="https://wa.me/41763652300"
                  style={{ textDecoration: "none" }}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<BsWhatsapp size={24} />}
                    fullWidth
                  >
                    WhatsApp öffnen
                  </Button>
                </a>

                {/* Additional Contact Info */}
                <Box
                  sx={{
                    mt: 4,
                    pt: 3,
                    borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: "text.secondary",
                      fontSize: pxToRem(14),
                      lineHeight: 1.5,
                      mb: 1,
                    }}
                  >
                    Oder rufe uns direkt an:
                  </Typography>
                  <a
                    href="tel:+41763652300"
                    style={{
                      textDecoration: "none",
                      color: "#67f756",
                      fontWeight: 600,
                      fontSize: pxToRem(16),
                    }}
                  >
                    +41 76 365 23 00
                  </a>
                </Box>
              </Paper>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default ImageContactSection;
