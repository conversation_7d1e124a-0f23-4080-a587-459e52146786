import { Box, Container, Grid, Typography, Button } from "@mui/material";
import Image from "next/image";
import React from "react";
import pxToRem from "@/ThemeRegistry/pxToRem";
import Link from "next/link";

function ImageGridSection() {
  return (
    <Box
      component="section"
      sx={{
        position: "relative",
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.12) 0%, transparent 50%),
          url(https://images.ctfassets.net/87jhdyn6f199/2klbM9zIZ9qFPUUuCpmfyl/e3a303a5266a74cb58d87ca5795789ee/hookah.png),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
        backgroundAttachment: { md: "fixed" },
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "center",
        py: { xs: 10, md: 16 },
        overflow: "hidden",
      }}
    >
      {/* Modern overlay with better opacity control */}
      <Box
        sx={{
          position: "absolute",
          inset: 0,
          background: `
            linear-gradient(135deg,
              rgba(9, 9, 11, 0.85) 0%,
              rgba(15, 15, 15, 0.75) 50%,
              rgba(9, 9, 11, 0.85) 100%
            )
          `,
          zIndex: 1,
        }}
      />

      {/* Floating background elements */}
      <Box
        sx={{
          position: "absolute",
          top: "20%",
          left: "10%",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.04) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(60px)",
          animation: "float 15s ease-in-out infinite",
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: "15%",
          right: "15%",
          width: "250px",
          height: "250px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(50px)",
          animation: "float 12s ease-in-out infinite reverse",
          zIndex: 1,
        }}
      />

      <Container sx={{ position: "relative", zIndex: 2 }}>
        {/* Modern Header Section */}
        <Box sx={{ textAlign: "center", mb: { xs: 8, md: 12 } }}>
          {/* Modern badge */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              backgroundColor: "rgba(103, 247, 86, 0.1)",
              border: "1px solid rgba(103, 247, 86, 0.3)",
              borderRadius: "50px",
              px: 4,
              py: 1.5,
              mb: 4,
              backdropFilter: "blur(10px)",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#67f756",
                fontWeight: "600",
                fontSize: pxToRem(14),
                letterSpacing: "0.5px",
                textTransform: "uppercase",
              }}
            >
              Events & Feiern
            </Typography>
          </Box>

          <Typography
            variant="h1"
            component="h2"
            sx={{
              fontSize: `clamp(${pxToRem(32)}, 6vw, ${pxToRem(56)})`,
              fontWeight: 700,
              mb: 3,
              background: "linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              lineHeight: 1.1,
            }}
          >
            Feiere deine Events bei uns!
          </Typography>

          <Typography
            variant="h6"
            sx={{
              color: "text.secondary",
              fontSize: pxToRem(18),
              fontWeight: 400,
              maxWidth: "600px",
              mx: "auto",
              lineHeight: 1.6,
            }}
          >
            Unvergessliche Geburtstage und Partys in luxuriöser Atmosphäre
          </Typography>
        </Box>
        <Grid
          container
          spacing={{ xs: 6, md: 10 }}
          alignItems="center"
          sx={{ flexDirection: { xs: "column-reverse", md: "row" } }}
        >
          {/* Modern Image Gallery */}
          <Grid item xs={12} md={6}>
            <Box sx={{ position: "relative" }}>
              {/* Main large image */}
              <Box
                className="image-container"
                sx={{
                  borderRadius: "24px",
                  overflow: "hidden",
                  mb: 3,
                  background: "rgba(255, 255, 255, 0.03)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  transition: "all 0.4s ease-in-out",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: "0 25px 50px rgba(103, 247, 86, 0.15)",
                    border: "1px solid rgba(103, 247, 86, 0.3)",
                  },
                }}
              >
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/7ooWbJ06KiPYPrVxrKfNNi/35e992f2c15769cc80c822a764ecb537/IMG_0007.JPG"
                  alt="Vibes Rooftop Events"
                  fill
                  className="image"
                  style={{ borderRadius: "24px" }}
                />
                {/* Modern overlay effect */}
                <Box
                  sx={{
                    position: "absolute",
                    inset: 0,
                    background: `
                      linear-gradient(135deg,
                        rgba(103,247,86,0.08) 0%,
                        transparent 30%,
                        transparent 70%,
                        rgba(74,29,31,0.08) 100%
                      )
                    `,
                    borderRadius: "24px",
                    pointerEvents: "none",
                  }}
                />
              </Box>

              {/* Smaller images grid */}
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <Box
                    className="image-container"
                    sx={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      background: "rgba(255, 255, 255, 0.02)",
                      backdropFilter: "blur(8px)",
                      border: "1px solid rgba(255, 255, 255, 0.08)",
                      transition: "all 0.3s ease-in-out",
                      "&:hover": {
                        transform: "translateY(-4px) scale(1.02)",
                        boxShadow: "0 15px 30px rgba(103, 247, 86, 0.1)",
                        border: "1px solid rgba(103, 247, 86, 0.2)",
                      },
                    }}
                  >
                    <Image
                      src="https://images.ctfassets.net/87jhdyn6f199/4yYVyJ0L5tR2igWpjU5MZj/c28503ae9bb9143b8d4f4f7581c45758/photo_2023-11-06_12-27-04.jpg"
                      alt="Vibes Rooftop Atmosphere"
                      fill
                      className="image"
                      style={{ borderRadius: "20px" }}
                    />
                    <Box
                      sx={{
                        position: "absolute",
                        inset: 0,
                        background:
                          "linear-gradient(45deg, rgba(103,247,86,0.05) 0%, rgba(74,29,31,0.05) 100%)",
                        borderRadius: "20px",
                        pointerEvents: "none",
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box
                    className="image-container"
                    sx={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      background: "rgba(255, 255, 255, 0.02)",
                      backdropFilter: "blur(8px)",
                      border: "1px solid rgba(255, 255, 255, 0.08)",
                      transition: "all 0.3s ease-in-out",
                      "&:hover": {
                        transform: "translateY(-4px) scale(1.02)",
                        boxShadow: "0 15px 30px rgba(103, 247, 86, 0.1)",
                        border: "1px solid rgba(103, 247, 86, 0.2)",
                      },
                    }}
                  >
                    <Image
                      src="https://images.ctfassets.net/87jhdyn6f199/4vehjxrR07huEAfiAAgV05/416c531c3f923598a7ecb4656baf6ff2/drink.jpg"
                      alt="Vibes Rooftop Drinks"
                      fill
                      className="image"
                      style={{ borderRadius: "20px" }}
                    />
                    <Box
                      sx={{
                        position: "absolute",
                        inset: 0,
                        background:
                          "linear-gradient(-45deg, rgba(74,29,31,0.05) 0%, rgba(103,247,86,0.05) 100%)",
                        borderRadius: "20px",
                        pointerEvents: "none",
                      }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Grid>
          {/* Modern Content Section */}
          <Grid item xs={12} md={6}>
            <Box sx={{ pl: { md: 4 } }}>
              {/* First content block */}
              <Box sx={{ mb: 6 }}>
                <Typography
                  variant="h3"
                  sx={{
                    fontSize: `clamp(${pxToRem(24)}, 4vw, ${pxToRem(32)})`,
                    fontWeight: 700,
                    mb: 3,
                    background:
                      "linear-gradient(135deg, #67f756 0%, #5ce646 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    lineHeight: 1.3,
                  }}
                >
                  Unvergessliche Events bei Vibes Rooftop
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                    mb: 2,
                  }}
                >
                  Mach deinen besonderen Tag unvergesslich bei Vibes Rooftop!
                  Unsere luxuriöse Atmosphäre, gepaart mit fairen Preisen und
                  exzellentem Service, bietet den perfekten Rahmen für deine
                  Geburtstagsfeier oder private Party.
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                  }}
                >
                  Erlebe unbeschwerte Stunden in einer einzigartigen Umgebung
                  auf unserer Dachterrasse, während wir uns um alles kümmern,
                  damit du und deine Gäste entspannt feiern könnt.
                </Typography>
              </Box>

              {/* Second content block */}
              <Box sx={{ mb: 6 }}>
                <Typography
                  variant="h3"
                  sx={{
                    fontSize: `clamp(${pxToRem(24)}, 4vw, ${pxToRem(32)})`,
                    fontWeight: 700,
                    mb: 3,
                    background:
                      "linear-gradient(135deg, #67f756 0%, #5ce646 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    lineHeight: 1.3,
                  }}
                >
                  Exklusive Events mit Stil
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                    mb: 2,
                  }}
                >
                  Ob Firmenfeiern, Jubiläen oder private Anlässe – bei Vibes
                  Rooftop wird dein Event zu einem außergewöhnlichen Erlebnis.
                  Genieße die perfekte Kombination aus erstklassigem Service,
                  angenehmer Atmosphäre und atemberaubendem Ausblick.
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                  }}
                >
                  Mit uns werden deine besonderen Momente stilvoll und
                  einzigartig.
                </Typography>
              </Box>

              {/* Call to Action */}
              <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                <Link href="/kontakt" style={{ textDecoration: "none" }}>
                  <Button variant="contained">Event anfragen</Button>
                </Link>
                <Link href="/gallerie" style={{ textDecoration: "none" }}>
                  <Button variant="outlined">Galerie ansehen</Button>
                </Link>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default ImageGridSection;
