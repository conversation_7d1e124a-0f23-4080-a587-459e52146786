import HeroComponent from "@/components/Hero";
import Divider from "@mui/material/Divider";
import React from "react";
import ReserveForm from "@/components/forms/ReserveForm";
import Events from "@/components/Events";
import ReviewWidget from "@/components/ReviewWidget";
import Aktions from "@/components/Aktions";
import ImageGridSection from "@/components/ImageGridSection";
import GridSection from "@/components/GridSection";
import ImageContactSection from "@/components/ImageContactSection";

export default async function Home() {
  return (
    <>
      <HeroComponent />
      <Events />
      <ReserveForm />
      {/* <Divider /> */}
      {/* https://images.ctfassets.net/87jhdyn6f199/LnCtjV6fF1YuDLErImOdy/2e733b1476865b1304ad23e0cdf9d1a8/winteraktion.jpg */}
      <Aktions />
      <Divider />
      <GridSection />
      {/* Modern review widget container */}
      <ReviewWidget />
      <ImageGridSection /> 
      <Divider />
      <ImageContactSection />
    </>
  );
}
